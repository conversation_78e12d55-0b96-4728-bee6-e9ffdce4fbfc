import pandas as pd
import numpy as np

def smoothrange(x, t, m):
    """Smooth Average Range"""
    wper = (t * 2) - 1
    avrng = abs(x - x.shift(1)).ewm(span=t, adjust=False).mean()
    return avrng.ewm(span=wper, adjust=False).mean() * m

def rangefilter(x, r):
    """Range Filter"""
    rf = x.copy()
    for i in range(1, len(x)):
        if x[i] > rf[i-1]:
            rf[i] = rf[i-1] if (x[i] - r[i]) < rf[i-1] else (x[i] - r[i])
        else:
            rf[i] = rf[i-1] if (x[i] + r[i]) > rf[i-1] else (x[i] + r[i])
    return rf

def smart_labelling_range_filter(df, price_col='close', mult=.382, blabels=True):
    """
    Python implementation of the Smart Labelling - Range Filter strategy.

    Args:
        df (pd.DataFrame): DataFrame containing the price data.
        price_col (str): Name of the column containing the price data.
        mult (float): Range Multiplier.
        blabels (bool): Show Labels (not implemented in Python).

    Returns:
        pd.DataFrame: DataFrame with the calculated indicators.
    """

    price = df[price_col]
    smthr = smoothrange(price, 1440, mult)
    rfilt = rangefilter(price, smthr)

    upward = pd.Series(np.zeros(len(df)))
    downward = pd.Series(np.zeros(len(df)))

    for i in range(1, len(df)):
        if rfilt[i] > rfilt[i-1]:
            upward[i] = upward[i-1] + 1 if not pd.isna(upward[i-1]) else 1
        elif rfilt[i] < rfilt[i-1]:
            upward[i] = 0
        else:
            upward[i] = upward[i-1] if not pd.isna(upward[i-1]) else 0

        if rfilt[i] < rfilt[i-1]:
            downward[i] = downward[i-1] + 1 if not pd.isna(downward[i-1]) else 1
        elif rfilt[i] > rfilt[i-1]:
            downward[i] = 0
        else:
            downward[i] = downward[i-1] if not pd.isna(downward[i-1]) else 0

    state = pd.Series(np.zeros(len(df)))
    for i in range(1, len(df)):
        if upward[i]:
            state[i] = 1
        elif downward[i]:
            state[i] = -1
        else:
            state[i] = state[i-1] if not pd.isna(state[i-1]) else 0

    long = pd.Series(np.zeros(len(df), dtype=bool))
    short = pd.Series(np.zeros(len(df), dtype=bool))

    for i in range(1, len(df)):
        long[i] = (state[i] != state[i-1]) and (state[i-1] == -1) if not pd.isna(state[i-1]) else False
        short[i] = (state[i] != state[i-1]) and (state[i-1] == 1) if not pd.isna(state[i-1]) else False

    df['smthr'] = smthr
    df['rfilt'] = rfilt
    df['upward'] = upward
    df['downward'] = downward
    df['state'] = state
    df['long'] = long
    df['short'] = short

    return df

if __name__ == '__main__':
    # Example usage with real data
    file_path = 'stpRNG_1min.csv'
    try:
        df = pd.read_csv(file_path)
        print(df.head())  # Print the first 5 rows
        df = smart_labelling_range_filter(df, price_col='close')
        long_signals = df['long'].sum()
        short_signals = df['short'].sum()
        print(f"Number of long signals: {long_signals}")
        print(f"Number of short signals: {short_signals}")

        # Calculate average bars in profit between buy and sell signals
        long_indices = df[df['long']].index
        short_indices = df[df['short']].index
        
        if len(long_indices) > 0 and len(short_indices) > 0:
            trade_durations = []
            profits = []

            # Define lot size parameters
            min_lot_size = 0.20
            max_lot_size = 50
            total_lot_limit = 200

            # Define Kelly Fraction
            kelly_fraction = 0.45

            total_profit = 0  # Initialize total profit

            for i in range(len(long_indices)):
                long_index = long_indices[i]

                # Entry is at the mid-point of the bar AFTER the signal bar (more realistic)
                entry_index = long_index + 1

                # Skip if entry bar doesn't exist
                if entry_index >= len(df):
                    continue

                # Find the next short signal after the current long signal
                next_short_index = short_indices[short_indices > long_index]
                if len(next_short_index) > 0:
                    next_short_index = next_short_index[0]

                    # Exit is at the mid-point of the bar AFTER the exit signal bar (more realistic)
                    exit_index = next_short_index + 1

                    # Skip if exit bar doesn't exist
                    if exit_index >= len(df):
                        continue

                    # Calculate the duration of the trade (from entry to exit)
                    trade_duration = exit_index - entry_index
                    trade_durations.append(trade_duration)

                    # Calculate entry and exit prices at mid-point of bars AFTER signals
                    entry_price = (df['open'][entry_index] + df['close'][entry_index]) / 2
                    exit_price = (df['open'][exit_index] + df['close'][exit_index]) / 2

                    # Calculate the profit of the trade (close price at short - mid-candle price at long)
                    profit_per_trade = exit_price - entry_price

                    # Calculate position size based on Kelly Fraction
                    # Assuming a potential loss is the same as potential profit for simplicity
                    fractional_kelly = kelly_fraction * (profit_per_trade / abs(profit_per_trade)) if profit_per_trade != 0 else 0

                    # Calculate lot size, capped by min_lot_size, max_lot_size and total_lot_limit
                    lot_size = fractional_kelly * total_lot_limit
                    lot_size = max(min_lot_size, min(lot_size, max_lot_size))

                    # Calculate profit for the trade
                    profit = profit_per_trade * lot_size
                    profits.append(profit)

                    total_profit += profit  # Accumulate total profit

            if len(trade_durations) > 0:
                avg_trade_duration = np.mean(trade_durations)
                avg_profit = np.mean(profits)
                print(f"Average bars in trade: {avg_trade_duration:.2f}")
                print(f"Average profit per trade: {avg_profit:.2f}")

                # Calculate Win Rate (WR)
                winning_trades = [p for p in profits if p > 0]
                losing_trades = [p for p in profits if p < 0]
                total_trades = len(profits)
                win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0

                # Calculate Profit Factor (PF)
                gross_profit = sum(winning_trades) if winning_trades else 0
                gross_loss = abs(sum(losing_trades)) if losing_trades else 0
                profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0

                # Calculate Sharpe Ratio
                if len(profits) > 1:
                    returns_std = np.std(profits, ddof=1)
                    sharpe_ratio = avg_profit / returns_std if returns_std > 0 else 0
                else:
                    sharpe_ratio = 0

                print(f"Win Rate (WR): {win_rate:.2f}%")
                print(f"Profit Factor (PF): {profit_factor:.2f}")
                print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
                print(f"Winning trades: {len(winning_trades)}")
                print(f"Losing trades: {len(losing_trades)}")
                print(f"Gross profit: {gross_profit:.2f}")
                print(f"Gross loss: {gross_loss:.2f}")
            else:
                print("No complete trades found.")

            # Print total profit
            print(f"Total profit: {total_profit:.2f}")
        else:
            print("Not enough long or short signals to calculate average bars in profit.")

    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")
