import pandas as pd
import numpy as np

def smoothrange(x, t, m):
    """Smooth Average Range"""
    wper = (t * 2) - 1
    avrng = abs(x - x.shift(1)).ewm(span=t, adjust=False).mean()
    return avrng.ewm(span=wper, adjust=False).mean() * m

def rangefilter(x, r):
    """Range Filter"""
    rf = x.copy()
    for i in range(1, len(x)):
        if x[i] > rf[i-1]:
            rf[i] = rf[i-1] if (x[i] - r[i]) < rf[i-1] else (x[i] - r[i])
        else:
            rf[i] = rf[i-1] if (x[i] + r[i]) > rf[i-1] else (x[i] + r[i])
    return rf

def smart_labelling_range_filter(df, price_col='close', mult=.382, blabels=True):
    """Generate signals using Smart Labelling Range Filter"""
    price = df[price_col]
    smthr = smoothrange(price, 1440, mult)
    rfilt = rangefilter(price, smthr)

    upward = pd.Series(np.zeros(len(df)))
    downward = pd.Series(np.zeros(len(df)))

    for i in range(1, len(df)):
        if rfilt[i] > rfilt[i-1]:
            upward[i] = upward[i-1] + 1 if not pd.isna(upward[i-1]) else 1
        elif rfilt[i] < rfilt[i-1]:
            upward[i] = 0
        else:
            upward[i] = upward[i-1] if not pd.isna(upward[i-1]) else 0

        if rfilt[i] < rfilt[i-1]:
            downward[i] = downward[i-1] + 1 if not pd.isna(downward[i-1]) else 1
        elif rfilt[i] > rfilt[i-1]:
            downward[i] = 0
        else:
            downward[i] = downward[i-1] if not pd.isna(downward[i-1]) else 0

    state = pd.Series(np.zeros(len(df)))
    for i in range(1, len(df)):
        if upward[i]:
            state[i] = 1
        elif downward[i]:
            state[i] = -1
        else:
            state[i] = state[i-1] if not pd.isna(state[i-1]) else 0

    long = pd.Series(np.zeros(len(df), dtype=bool))
    short = pd.Series(np.zeros(len(df), dtype=bool))

    for i in range(1, len(df)):
        long[i] = (state[i] != state[i-1]) and (state[i-1] == -1) if not pd.isna(state[i-1]) else False
        short[i] = (state[i] != state[i-1]) and (state[i-1] == 1) if not pd.isna(state[i-1]) else False

    df['long'] = long
    df['short'] = short
    return df

def analyze_trade_progression(df, signal_index, signal_type, exit_bars=6):
    """Analyze how a trade progresses bar by bar"""
    entry_index = signal_index + 1
    
    if entry_index + exit_bars >= len(df):
        return None
    
    entry_price = df['open'][entry_index]
    trade_data = {
        'signal_type': signal_type,
        'signal_index': signal_index,
        'entry_index': entry_index,
        'entry_price': entry_price,
        'bar_prices': [],
        'bar_pnl': [],
        'max_favorable': 0,
        'max_adverse': 0,
        'final_pnl': 0
    }
    
    # Track price movement for each bar
    for bar in range(1, exit_bars + 1):
        current_index = entry_index + bar
        current_price = df['open'][current_index]
        
        if signal_type == 'long':
            pnl = current_price - entry_price
        else:  # short
            pnl = entry_price - current_price
            
        trade_data['bar_prices'].append(current_price)
        trade_data['bar_pnl'].append(pnl)
        
        # Track maximum favorable and adverse excursions
        if pnl > trade_data['max_favorable']:
            trade_data['max_favorable'] = pnl
        if pnl < trade_data['max_adverse']:
            trade_data['max_adverse'] = pnl
    
    trade_data['final_pnl'] = trade_data['bar_pnl'][-1]
    return trade_data

def detailed_loss_analysis(df, exit_bars=6):
    """Perform detailed analysis of losing trades"""
    
    print(f"DETAILED LOSS ANALYSIS - {exit_bars} Bar Exit Strategy")
    print("="*70)
    
    long_indices = df[df['long']].index
    short_indices = df[df['short']].index
    
    all_trades = []
    losing_trades = []
    winning_trades = []
    
    # Analyze long trades
    for long_index in long_indices:
        trade_data = analyze_trade_progression(df, long_index, 'long', exit_bars)
        if trade_data:
            all_trades.append(trade_data)
            if trade_data['final_pnl'] < 0:
                losing_trades.append(trade_data)
            else:
                winning_trades.append(trade_data)
    
    # Analyze short trades
    for short_index in short_indices:
        trade_data = analyze_trade_progression(df, short_index, 'short', exit_bars)
        if trade_data:
            all_trades.append(trade_data)
            if trade_data['final_pnl'] < 0:
                losing_trades.append(trade_data)
            else:
                winning_trades.append(trade_data)
    
    print(f"Total trades analyzed: {len(all_trades)}")
    print(f"Winning trades: {len(winning_trades)} ({len(winning_trades)/len(all_trades)*100:.2f}%)")
    print(f"Losing trades: {len(losing_trades)} ({len(losing_trades)/len(all_trades)*100:.2f}%)")
    
    # Categorize losing trades
    immediate_losses = []  # Never profitable
    delayed_losses = []    # Profitable initially, then turned negative
    small_losses = []      # Loss < 0.5 points
    medium_losses = []     # Loss 0.5-2.0 points
    large_losses = []      # Loss > 2.0 points
    
    for trade in losing_trades:
        final_loss = abs(trade['final_pnl'])
        
        # Categorize by magnitude
        if final_loss < 0.5:
            small_losses.append(trade)
        elif final_loss <= 2.0:
            medium_losses.append(trade)
        else:
            large_losses.append(trade)
        
        # Categorize by progression
        if trade['max_favorable'] <= 0:
            immediate_losses.append(trade)
        else:
            delayed_losses.append(trade)
    
    print(f"\nLOSS CATEGORIZATION:")
    print("-"*40)
    print(f"Immediate losses (never profitable): {len(immediate_losses)} ({len(immediate_losses)/len(losing_trades)*100:.1f}%)")
    print(f"Delayed losses (profitable then negative): {len(delayed_losses)} ({len(delayed_losses)/len(losing_trades)*100:.1f}%)")
    
    print(f"\nLOSS MAGNITUDE:")
    print("-"*40)
    print(f"Small losses (<0.5 pts): {len(small_losses)} ({len(small_losses)/len(losing_trades)*100:.1f}%)")
    print(f"Medium losses (0.5-2.0 pts): {len(medium_losses)} ({len(medium_losses)/len(losing_trades)*100:.1f}%)")
    print(f"Large losses (>2.0 pts): {len(large_losses)} ({len(large_losses)/len(losing_trades)*100:.1f}%)")
    
    # Average loss statistics
    avg_loss = np.mean([abs(t['final_pnl']) for t in losing_trades])
    avg_max_adverse = np.mean([abs(t['max_adverse']) for t in losing_trades])
    avg_max_favorable_in_losers = np.mean([t['max_favorable'] for t in delayed_losses]) if delayed_losses else 0
    
    print(f"\nLOSS STATISTICS:")
    print("-"*40)
    print(f"Average loss magnitude: {avg_loss:.3f} points")
    print(f"Average maximum adverse excursion: {avg_max_adverse:.3f} points")
    print(f"Average max favorable in delayed losses: {avg_max_favorable_in_losers:.3f} points")
    
    # Analyze bar-by-bar progression for losing trades
    print(f"\nBAR-BY-BAR LOSS PROGRESSION:")
    print("-"*40)
    
    for bar in range(exit_bars):
        losses_at_bar = [t['bar_pnl'][bar] for t in losing_trades if t['bar_pnl'][bar] < 0]
        if losses_at_bar:
            print(f"Bar {bar+1}: {len(losses_at_bar)} trades negative, avg loss: {np.mean([abs(x) for x in losses_at_bar]):.3f} pts")
    
    # Show worst losing trades
    print(f"\nWORST 10 LOSING TRADES:")
    print("-"*40)
    worst_trades = sorted(losing_trades, key=lambda x: x['final_pnl'])[:10]
    
    for i, trade in enumerate(worst_trades, 1):
        print(f"{i:2d}. {trade['signal_type'].upper()} signal at bar {trade['signal_index']}: "
              f"Final loss: {abs(trade['final_pnl']):.3f} pts, "
              f"Max adverse: {abs(trade['max_adverse']):.3f} pts, "
              f"Max favorable: {trade['max_favorable']:.3f} pts")
    
    # Compare with winning trades
    avg_win = np.mean([t['final_pnl'] for t in winning_trades]) if winning_trades else 0
    avg_max_favorable_in_winners = np.mean([t['max_favorable'] for t in winning_trades]) if winning_trades else 0
    
    print(f"\nWINNING TRADE COMPARISON:")
    print("-"*40)
    print(f"Average winning trade: {avg_win:.3f} points")
    print(f"Average max favorable in winners: {avg_max_favorable_in_winners:.3f} points")
    print(f"Win/Loss ratio: {avg_win/avg_loss:.2f}" if avg_loss > 0 else "N/A")
    
    return {
        'total_trades': len(all_trades),
        'losing_trades': len(losing_trades),
        'immediate_losses': len(immediate_losses),
        'delayed_losses': len(delayed_losses),
        'small_losses': len(small_losses),
        'medium_losses': len(medium_losses),
        'large_losses': len(large_losses),
        'avg_loss': avg_loss,
        'avg_win': avg_win
    }

if __name__ == '__main__':
    # Load data
    file_path = 'stpRNG_1min.csv'
    try:
        df = pd.read_csv(file_path)
        print(f"Loaded {len(df)} bars of data")
        
        # Generate signals
        df = smart_labelling_range_filter(df, price_col='close')
        
        # Perform detailed loss analysis for 6-bar exit (best win rate)
        results = detailed_loss_analysis(df, exit_bars=6)
        
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")
