import pandas as pd
import numpy as np

def smoothrange(x, t, m):
    """Smooth Average Range"""
    wper = (t * 2) - 1
    avrng = abs(x - x.shift(1)).ewm(span=t, adjust=False).mean()
    return avrng.ewm(span=wper, adjust=False).mean() * m

def rangefilter(x, r):
    """Range Filter"""
    rf = x.copy()
    for i in range(1, len(x)):
        if x[i] > rf[i-1]:
            rf[i] = rf[i-1] if (x[i] - r[i]) < rf[i-1] else (x[i] - r[i])
        else:
            rf[i] = rf[i-1] if (x[i] + r[i]) > rf[i-1] else (x[i] + r[i])
    return rf

def smart_labelling_range_filter(df, price_col='close', mult=.382, blabels=True):
    """Generate signals using Smart Labelling Range Filter"""
    price = df[price_col]
    smthr = smoothrange(price, 1440, mult)
    rfilt = rangefilter(price, smthr)

    upward = pd.Series(np.zeros(len(df)))
    downward = pd.Series(np.zeros(len(df)))

    for i in range(1, len(df)):
        if rfilt[i] > rfilt[i-1]:
            upward[i] = upward[i-1] + 1 if not pd.isna(upward[i-1]) else 1
        elif rfilt[i] < rfilt[i-1]:
            upward[i] = 0
        else:
            upward[i] = upward[i-1] if not pd.isna(upward[i-1]) else 0

        if rfilt[i] < rfilt[i-1]:
            downward[i] = downward[i-1] + 1 if not pd.isna(downward[i-1]) else 1
        elif rfilt[i] > rfilt[i-1]:
            downward[i] = 0
        else:
            downward[i] = downward[i-1] if not pd.isna(downward[i-1]) else 0

    state = pd.Series(np.zeros(len(df)))
    for i in range(1, len(df)):
        if upward[i]:
            state[i] = 1
        elif downward[i]:
            state[i] = -1
        else:
            state[i] = state[i-1] if not pd.isna(state[i-1]) else 0

    long = pd.Series(np.zeros(len(df), dtype=bool))
    short = pd.Series(np.zeros(len(df), dtype=bool))

    for i in range(1, len(df)):
        long[i] = (state[i] != state[i-1]) and (state[i-1] == -1) if not pd.isna(state[i-1]) else False
        short[i] = (state[i] != state[i-1]) and (state[i-1] == 1) if not pd.isna(state[i-1]) else False

    df['long'] = long
    df['short'] = short
    return df

def analyze_fixed_exit_timing(df, exit_bars_list):
    """Analyze performance for different fixed exit timings"""
    
    results = []
    
    for exit_bars in exit_bars_list:
        print(f"\nAnalyzing {exit_bars} bar exit timing...")
        
        long_indices = df[df['long']].index
        short_indices = df[df['short']].index
        
        # Combine long and short signals for analysis
        all_signals = []
        
        # Process long signals
        for long_index in long_indices:
            entry_index = long_index + 1  # Enter at open of next bar
            exit_index = entry_index + exit_bars  # Exit after fixed bars
            
            if entry_index < len(df) and exit_index < len(df):
                entry_price = df['open'][entry_index]
                exit_price = df['open'][exit_index]
                profit_per_trade = exit_price - entry_price  # Long trade
                all_signals.append(profit_per_trade)
        
        # Process short signals  
        for short_index in short_indices:
            entry_index = short_index + 1  # Enter at open of next bar
            exit_index = entry_index + exit_bars  # Exit after fixed bars
            
            if entry_index < len(df) and exit_index < len(df):
                entry_price = df['open'][entry_index]
                exit_price = df['open'][exit_index]
                profit_per_trade = entry_price - exit_price  # Short trade (reversed)
                all_signals.append(profit_per_trade)
        
        if len(all_signals) > 0:
            # Calculate metrics
            profits_with_lots = []
            min_lot_size = 0.20
            max_lot_size = 50
            total_lot_limit = 200
            kelly_fraction = 0.45
            
            for profit_per_trade in all_signals:
                # Calculate lot size
                fractional_kelly = kelly_fraction * (profit_per_trade / abs(profit_per_trade)) if profit_per_trade != 0 else 0
                lot_size = fractional_kelly * total_lot_limit
                lot_size = max(min_lot_size, min(lot_size, max_lot_size))
                
                # Calculate profit in dollars
                profit_dollars = profit_per_trade * lot_size * 1.0
                profits_with_lots.append(profit_dollars)
            
            # Calculate performance metrics
            winning_trades = [p for p in profits_with_lots if p > 0]
            losing_trades = [p for p in profits_with_lots if p < 0]
            total_trades = len(profits_with_lots)
            
            win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0
            
            gross_profit = sum(winning_trades) if winning_trades else 0
            gross_loss = abs(sum(losing_trades)) if losing_trades else 0
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0
            
            total_profit = sum(profits_with_lots)
            avg_profit = np.mean(profits_with_lots)
            
            # Store results
            results.append({
                'exit_bars': exit_bars,
                'total_trades': total_trades,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'total_profit': total_profit,
                'avg_profit': avg_profit,
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades)
            })
            
            print(f"Total trades: {total_trades}")
            print(f"Win rate: {win_rate:.2f}%")
            print(f"Profit factor: {profit_factor:.2f}")
            print(f"Total profit: ${total_profit:.2f}")
            print(f"Avg profit per trade: ${avg_profit:.2f}")
    
    return results

if __name__ == '__main__':
    # Load data
    file_path = 'stpRNG_1min.csv'
    try:
        df = pd.read_csv(file_path)
        print(f"Loaded {len(df)} bars of data")
        
        # Generate signals
        df = smart_labelling_range_filter(df, price_col='close')
        
        long_signals = df['long'].sum()
        short_signals = df['short'].sum()
        print(f"Long signals: {long_signals}")
        print(f"Short signals: {short_signals}")
        
        # Test different exit timings
        exit_bars_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        results = analyze_fixed_exit_timing(df, exit_bars_list)
        
        # Summary table
        print("\n" + "="*80)
        print("SUMMARY OF FIXED EXIT TIMING ANALYSIS")
        print("="*80)
        print(f"{'Bars':<5} {'Trades':<7} {'WR%':<7} {'PF':<8} {'Total $':<12} {'Avg $':<8}")
        print("-"*80)
        
        for result in results:
            print(f"{result['exit_bars']:<5} {result['total_trades']:<7} "
                  f"{result['win_rate']:<7.2f} {result['profit_factor']:<8.2f} "
                  f"{result['total_profit']:<12.2f} {result['avg_profit']:<8.2f}")
        
        # Find best combinations
        print("\n" + "="*50)
        print("BEST PERFORMERS:")
        print("="*50)
        
        # Best win rate
        best_wr = max(results, key=lambda x: x['win_rate'])
        print(f"Best Win Rate: {best_wr['exit_bars']} bars - {best_wr['win_rate']:.2f}% WR, {best_wr['profit_factor']:.2f} PF")
        
        # Best profit factor
        valid_pf_results = [r for r in results if r['profit_factor'] != float('inf')]
        if valid_pf_results:
            best_pf = max(valid_pf_results, key=lambda x: x['profit_factor'])
            print(f"Best Profit Factor: {best_pf['exit_bars']} bars - {best_pf['win_rate']:.2f}% WR, {best_pf['profit_factor']:.2f} PF")
        
        # Best total profit
        best_profit = max(results, key=lambda x: x['total_profit'])
        print(f"Best Total Profit: {best_profit['exit_bars']} bars - ${best_profit['total_profit']:.2f}, {best_profit['win_rate']:.2f}% WR")
        
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")
