import pandas as pd
import numpy as np

def convert_renko_0_1_to_1_0(input_file, output_file):
    """
    Convert 0.1 point Renko bars to 1.0 point Renko bars.
    Each 1.0 point bar represents 10 consecutive 0.1 point moves.
    
    Args:
        input_file (str): Path to input 0.1 Renko CSV file
        output_file (str): Path to output 1.0 Renko CSV file
    """
    print(f"Loading 0.1 point Renko data from {input_file}...")
    
    # Load the 0.1 point Renko data
    df = pd.read_csv(input_file)
    
    print(f"Loaded {len(df):,} 0.1-point Renko bars")
    print(f"Date range: {df['datetime'].iloc[0]} to {df['datetime'].iloc[-1]}")
    print(f"Price range: {df['low'].min():.1f} to {df['high'].max():.1f}")
    
    # Convert datetime to pandas datetime for easier manipulation
    df['datetime'] = pd.to_datetime(df['datetime'])
    
    # Create 1.0 point Ren<PERSON> bars
    print("Converting to 1.0 point Renko bars...")
    
    renko_1_0_bars = []
    
    # Starting values
    current_open = df['open'].iloc[0]
    current_high = df['high'].iloc[0]
    current_low = df['low'].iloc[0]
    current_close = df['close'].iloc[0]
    start_timestamp = df['timestamp'].iloc[0]
    start_datetime = df['datetime'].iloc[0]
    
    # Track cumulative movement
    cumulative_movement = 0
    bar_count = 0
    
    for i in range(len(df)):
        row = df.iloc[i]
        
        # Update high and low for current 1.0 bar
        current_high = max(current_high, row['high'])
        current_low = min(current_low, row['low'])
        
        # Determine movement direction and magnitude
        if row['direction'] == 'up':
            cumulative_movement += 0.1
        elif row['direction'] == 'down':
            cumulative_movement -= 0.1
        
        bar_count += 1
        
        # Check if we've accumulated 1.0 point movement
        if abs(cumulative_movement) >= 1.0:
            # Determine direction of the 1.0 bar
            direction = 'up' if cumulative_movement > 0 else 'down'
            
            # Calculate close price based on direction
            if direction == 'up':
                current_close = current_open + 1.0
            else:
                current_close = current_open - 1.0
            
            # Create the 1.0 point bar
            renko_1_0_bars.append({
                'timestamp': start_timestamp,
                'datetime': start_datetime,
                'open': current_open,
                'high': current_high,
                'low': current_low,
                'close': current_close,
                'direction': direction,
                'bars_aggregated': bar_count
            })
            
            # Reset for next 1.0 bar
            current_open = current_close
            current_high = current_close
            current_low = current_close
            start_timestamp = row['timestamp']
            start_datetime = row['datetime']
            cumulative_movement = 0
            bar_count = 0
    
    # Convert to DataFrame
    renko_1_0_df = pd.DataFrame(renko_1_0_bars)
    
    # Remove the bars_aggregated column for final output (just for analysis)
    bars_aggregated_info = renko_1_0_df['bars_aggregated'].copy()
    renko_1_0_df = renko_1_0_df.drop('bars_aggregated', axis=1)
    
    print(f"Created {len(renko_1_0_df):,} 1.0-point Renko bars")
    print(f"Compression ratio: {len(df) / len(renko_1_0_df):.1f}:1")
    print(f"Average 0.1 bars per 1.0 bar: {bars_aggregated_info.mean():.1f}")
    print(f"Min/Max 0.1 bars per 1.0 bar: {bars_aggregated_info.min()}/{bars_aggregated_info.max()}")
    
    # Save to CSV
    print(f"Saving 1.0-point Renko bars to {output_file}...")
    renko_1_0_df.to_csv(output_file, index=False)
    
    print("Conversion completed successfully!")
    print(f"Output file: {output_file}")
    print(f"Time range: {renko_1_0_df['datetime'].iloc[0]} to {renko_1_0_df['datetime'].iloc[-1]}")
    
    # Show sample of converted data
    print("\nSample of converted 1.0-point Renko data:")
    print(renko_1_0_df.head(10))
    
    # Verify the conversion
    print(f"\nVerification:")
    print(f"Original 0.1 bars: {len(df):,}")
    print(f"New 1.0 bars: {len(renko_1_0_df):,}")
    print(f"Expected theoretical ratio: ~10:1")
    print(f"Actual ratio: {len(df) / len(renko_1_0_df):.1f}:1")
    
    # Direction distribution
    up_bars = len(renko_1_0_df[renko_1_0_df['direction'] == 'up'])
    down_bars = len(renko_1_0_df[renko_1_0_df['direction'] == 'down'])
    print(f"Up bars: {up_bars} ({up_bars/len(renko_1_0_df)*100:.1f}%)")
    print(f"Down bars: {down_bars} ({down_bars/len(renko_1_0_df)*100:.1f}%)")
    
    return renko_1_0_df

if __name__ == '__main__':
    # Convert the 3-day Step Index Renko data from 0.1 to 1.0 point bars
    input_file = 'step_index_renko_0_1_3days.csv'
    output_file = 'step_index_renko_1_0_3days.csv'
    
    try:
        result_df = convert_renko_0_1_to_1_0(input_file, output_file)
        
        print(f"\nConversion Summary:")
        print(f"Input: {input_file}")
        print(f"Output: {output_file}")
        print(f"Conversion: 0.1-point → 1.0-point Renko bars")
        print(f"Size reduction: {len(pd.read_csv(input_file)) / len(result_df):.1f}x smaller")
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
    except Exception as e:
        print(f"An error occurred: {e}")
