import pandas as pd
import numpy as np
from datetime import datetime

def convert_renko_to_1min_ohlc(input_file, output_file):
    """
    Convert Step Index Renko data to 1-minute OHLC format.
    
    Args:
        input_file (str): Path to input Renko CSV file
        output_file (str): Path to output 1-minute OHLC CSV file
    """
    print(f"Loading Renko data from {input_file}...")
    
    # Load the Renko data
    df = pd.read_csv(input_file)
    
    print(f"Loaded {len(df):,} Renko bars")
    print(f"Date range: {df['datetime'].iloc[0]} to {df['datetime'].iloc[-1]}")
    
    # Convert datetime column to pandas datetime
    df['datetime'] = pd.to_datetime(df['datetime'])
    
    # Set datetime as index for resampling
    df.set_index('datetime', inplace=True)
    
    # Resample to 1-minute OHLC bars
    print("Converting to 1-minute OHLC bars...")
    
    ohlc_1min = df.resample('1T').agg({
        'open': 'first',    # First price in the minute
        'high': 'max',      # Highest price in the minute
        'low': 'min',       # Lowest price in the minute
        'close': 'last',    # Last price in the minute
        'timestamp': 'first'  # Keep first timestamp of the minute
    }).dropna()  # Remove any empty minutes
    
    # Reset index to make datetime a column again
    ohlc_1min.reset_index(inplace=True)
    
    # Reorder columns to match typical OHLC format
    ohlc_1min = ohlc_1min[['datetime', 'timestamp', 'open', 'high', 'low', 'close']]
    
    # Save to CSV
    print(f"Saving {len(ohlc_1min):,} 1-minute bars to {output_file}...")
    ohlc_1min.to_csv(output_file, index=False)
    
    print("Conversion completed successfully!")
    print(f"Output file: {output_file}")
    print(f"1-minute bars created: {len(ohlc_1min):,}")
    print(f"Time range: {ohlc_1min['datetime'].iloc[0]} to {ohlc_1min['datetime'].iloc[-1]}")
    
    # Show sample of converted data
    print("\nSample of converted 1-minute OHLC data:")
    print(ohlc_1min.head(10))
    
    return ohlc_1min

if __name__ == '__main__':
    # Convert the Step Index Renko data to 1-minute OHLC
    input_file = 'step_index_renko_0_1_30days_fixed.csv'
    output_file = 'stpRNG_1min.csv'
    
    try:
        result_df = convert_renko_to_1min_ohlc(input_file, output_file)
        
        # Additional statistics
        print(f"\nData Statistics:")
        print(f"Total trading minutes: {len(result_df):,}")
        print(f"Average price: {result_df['close'].mean():.2f}")
        print(f"Price range: {result_df['low'].min():.2f} - {result_df['high'].max():.2f}")
        print(f"Total price movement: {result_df['high'].max() - result_df['low'].min():.2f}")
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
    except Exception as e:
        print(f"An error occurred: {e}")
