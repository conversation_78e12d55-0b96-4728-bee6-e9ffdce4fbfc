import pandas as pd
import numpy as np

def smoothrange(x, t, m):
    """Smooth Average Range"""
    wper = (t * 2) - 1
    avrng = abs(x - x.shift(1)).ewm(span=t, adjust=False).mean()
    return avrng.ewm(span=wper, adjust=False).mean() * m

def rangefilter(x, r):
    """Range Filter"""
    rf = x.copy()
    for i in range(1, len(x)):
        if x[i] > rf[i-1]:
            rf[i] = rf[i-1] if (x[i] - r[i]) < rf[i-1] else (x[i] - r[i])
        else:
            rf[i] = rf[i-1] if (x[i] + r[i]) > rf[i-1] else (x[i] + r[i])
    return rf

def smart_labelling_range_filter(df, price_col='close', mult=.382, blabels=True):
    """
    Python implementation of the Smart Labelling - Range Filter strategy.
    """
    price = df[price_col]
    smthr = smoothrange(price, 1440, mult)
    rfilt = rangefilter(price, smthr)

    upward = pd.Series(np.zeros(len(df)))
    downward = pd.Series(np.zeros(len(df)))

    for i in range(1, len(df)):
        if rfilt[i] > rfilt[i-1]:
            upward[i] = upward[i-1] + 1 if not pd.isna(upward[i-1]) else 1
        elif rfilt[i] < rfilt[i-1]:
            upward[i] = 0
        else:
            upward[i] = upward[i-1] if not pd.isna(upward[i-1]) else 0

        if rfilt[i] < rfilt[i-1]:
            downward[i] = downward[i-1] + 1 if not pd.isna(downward[i-1]) else 1
        elif rfilt[i] > rfilt[i-1]:
            downward[i] = 0
        else:
            downward[i] = downward[i-1] if not pd.isna(downward[i-1]) else 0

    state = pd.Series(np.zeros(len(df)))
    for i in range(1, len(df)):
        if upward[i]:
            state[i] = 1
        elif downward[i]:
            state[i] = -1
        else:
            state[i] = state[i-1] if not pd.isna(state[i-1]) else 0

    long = pd.Series(np.zeros(len(df), dtype=bool))
    short = pd.Series(np.zeros(len(df), dtype=bool))

    for i in range(1, len(df)):
        long[i] = (state[i] != state[i-1]) and (state[i-1] == -1) if not pd.isna(state[i-1]) else False
        short[i] = (state[i] != state[i-1]) and (state[i-1] == 1) if not pd.isna(state[i-1]) else False

    df['smthr'] = smthr
    df['rfilt'] = rfilt
    df['upward'] = upward
    df['downward'] = downward
    df['state'] = state
    df['long'] = long
    df['short'] = short

    return df

def backtest_fixed_exit(df, exit_bars=6):
    """
    Backtest with fixed exit timing on 1.0-point Renko data
    """
    print(f"BACKTESTING 1.0-POINT RENKO DATA - {exit_bars} Bar Exit")
    print("="*60)
    
    long_indices = df[df['long']].index
    short_indices = df[df['short']].index
    
    print(f"Long signals: {len(long_indices)}")
    print(f"Short signals: {len(short_indices)}")
    
    # Trading parameters
    min_lot_size = 0.20
    max_lot_size = 50
    total_lot_limit = 200
    kelly_fraction = 0.45
    starting_balance = 10.0
    
    all_trades = []
    total_profit = 0
    
    # Process long trades
    for long_index in long_indices:
        entry_index = long_index + 1
        exit_index = entry_index + exit_bars
        
        if entry_index < len(df) and exit_index < len(df):
            entry_price = df['open'][entry_index]
            exit_price = df['open'][exit_index]
            profit_per_trade = exit_price - entry_price
            
            # Calculate lot size
            fractional_kelly = kelly_fraction * (profit_per_trade / abs(profit_per_trade)) if profit_per_trade != 0 else 0
            lot_size = fractional_kelly * total_lot_limit
            lot_size = max(min_lot_size, min(lot_size, max_lot_size))
            
            # Calculate profit in dollars (1.0 point = lot_size * $1.00)
            profit_dollars = profit_per_trade * lot_size * 1.0
            all_trades.append(profit_dollars)
            total_profit += profit_dollars
    
    # Process short trades
    for short_index in short_indices:
        entry_index = short_index + 1
        exit_index = entry_index + exit_bars
        
        if entry_index < len(df) and exit_index < len(df):
            entry_price = df['open'][entry_index]
            exit_price = df['open'][exit_index]
            profit_per_trade = entry_price - exit_price  # Reversed for short
            
            # Calculate lot size
            fractional_kelly = kelly_fraction * (profit_per_trade / abs(profit_per_trade)) if profit_per_trade != 0 else 0
            lot_size = fractional_kelly * total_lot_limit
            lot_size = max(min_lot_size, min(lot_size, max_lot_size))
            
            # Calculate profit in dollars
            profit_dollars = profit_per_trade * lot_size * 1.0
            all_trades.append(profit_dollars)
            total_profit += profit_dollars
    
    if len(all_trades) > 0:
        # Calculate performance metrics
        winning_trades = [p for p in all_trades if p > 0]
        losing_trades = [p for p in all_trades if p < 0]
        total_trades = len(all_trades)
        
        win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0
        
        gross_profit = sum(winning_trades) if winning_trades else 0
        gross_loss = abs(sum(losing_trades)) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf') if gross_profit > 0 else 0
        
        avg_profit = np.mean(all_trades)
        
        # Sharpe ratio
        if len(all_trades) > 1:
            returns_std = np.std(all_trades, ddof=1)
            sharpe_ratio = avg_profit / returns_std if returns_std > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Balance calculations
        final_balance = starting_balance + total_profit
        total_return_pct = (total_profit / starting_balance) * 100
        
        # Print results
        print(f"Total trades: {total_trades}")
        print(f"Average profit per trade: ${avg_profit:.2f}")
        print(f"Win Rate (WR): {win_rate:.2f}%")
        print(f"Profit Factor (PF): {profit_factor:.2f}")
        print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"Winning trades: {len(winning_trades)}")
        print(f"Losing trades: {len(losing_trades)}")
        print(f"Gross profit: ${gross_profit:.2f}")
        print(f"Gross loss: ${gross_loss:.2f}")
        print(f"Starting balance: ${starting_balance:.2f}")
        print(f"Total profit: ${total_profit:.2f}")
        print(f"Final balance: ${final_balance:.2f}")
        print(f"Total return: {total_return_pct:.2f}%")
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_profit': total_profit,
            'avg_profit': avg_profit,
            'sharpe_ratio': sharpe_ratio,
            'final_balance': final_balance,
            'total_return_pct': total_return_pct
        }
    else:
        print("No trades found.")
        return None

def analyze_multiple_exit_timings(df):
    """Analyze different exit timings for 1.0-point Renko data"""
    
    print("\nANALYZING MULTIPLE EXIT TIMINGS FOR 1.0-POINT RENKO")
    print("="*70)
    
    exit_bars_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    results = []
    
    for exit_bars in exit_bars_list:
        print(f"\n--- Testing {exit_bars} bar exit ---")
        result = backtest_fixed_exit(df, exit_bars)
        if result:
            result['exit_bars'] = exit_bars
            results.append(result)
    
    if results:
        print("\n" + "="*80)
        print("SUMMARY OF 1.0-POINT RENKO EXIT TIMING ANALYSIS")
        print("="*80)
        print(f"{'Bars':<5} {'Trades':<7} {'WR%':<7} {'PF':<8} {'Total $':<12} {'Return%':<10}")
        print("-"*80)
        
        for result in results:
            print(f"{result['exit_bars']:<5} {result['total_trades']:<7} "
                  f"{result['win_rate']:<7.2f} {result['profit_factor']:<8.2f} "
                  f"{result['total_profit']:<12.2f} {result['total_return_pct']:<10.2f}")
        
        # Find best performers
        print("\n" + "="*50)
        print("BEST PERFORMERS (1.0-POINT RENKO):")
        print("="*50)
        
        # Best win rate
        best_wr = max(results, key=lambda x: x['win_rate'])
        print(f"Best Win Rate: {best_wr['exit_bars']} bars - {best_wr['win_rate']:.2f}% WR, {best_wr['profit_factor']:.2f} PF")
        
        # Best profit factor
        valid_pf_results = [r for r in results if r['profit_factor'] != float('inf')]
        if valid_pf_results:
            best_pf = max(valid_pf_results, key=lambda x: x['profit_factor'])
            print(f"Best Profit Factor: {best_pf['exit_bars']} bars - {best_pf['win_rate']:.2f}% WR, {best_pf['profit_factor']:.2f} PF")
        
        # Best total return
        best_return = max(results, key=lambda x: x['total_return_pct'])
        print(f"Best Total Return: {best_return['exit_bars']} bars - {best_return['total_return_pct']:.2f}% return, {best_return['win_rate']:.2f}% WR")
    
    return results

if __name__ == '__main__':
    # Load 1.0-point Renko data
    file_path = 'step_index_renko_1_0_3days.csv'
    try:
        df = pd.read_csv(file_path)
        print(f"Loaded {len(df)} 1.0-point Renko bars")
        print(f"Date range: {df['datetime'].iloc[0]} to {df['datetime'].iloc[-1]}")
        print(f"Price range: {df['low'].min():.1f} to {df['high'].max():.1f}")
        
        # Generate signals
        df = smart_labelling_range_filter(df, price_col='close')
        
        # Run analysis for multiple exit timings
        results = analyze_multiple_exit_timings(df)
        
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")
